import React from 'react';

interface ChatHeaderProps {
  onClose: () => void;
  onClearSession: () => void;
  chatIconUrl?: string;
  isPortalMode?: boolean;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  onClose,
  onClearSession,
  chatIconUrl = "/quoteai.png",
  isPortalMode = false
}) => {
  return (
    <div className={`chat-header ${isPortalMode ? 'portal-mode' : ''}`}>
      <img src={chatIconUrl} alt="Chatbot Icon" className="chat-icon" />
      {isPortalMode ? 'Chat Support' : 'Chat with us!'}
      <div className="header-buttons">
        <button
          className="clear-button"
          onClick={onClearSession}
          title="Clear chat history and start new session"
          aria-label="Clear chat history"
        >
          🗑️
        </button>
        <button
          className="close-button"
          onClick={onClose}
          title={isPortalMode ? "Go back" : "Close chat"}
          aria-label={isPortalMode ? "Go back" : "Close chat"}
        >
          {isPortalMode ? '←' : '×'}
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;