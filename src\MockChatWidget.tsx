import React, { useState, useRef } from 'react';
import ChatHeader from './components/ChatWindow/ChatHeader';
import MessageList from './components/ChatWindow/MessageList';
import ChatInputArea from './components/ChatWindow/ChatInputArea';
import ImagePreviewArea from './components/ChatWindow/ImagePreviewArea';
import ExpandedImageView from './components/ChatWindow/ExpandedImageView';
import type { Message, Suggestion, ChatWidgetProps } from './types';

// Mock data for demonstration
const MOCK_MESSAGES: Message[] = [
  {
    type: 'bot',
    content: 'Hello! Welcome to our chat portal. I\'m here to help you get a quote for your project. What type of work do you need done?',
    timestamp: Date.now() - 60000,
    suggestions: [
      { text: 'Plumbing', value: 'I need plumbing work done' },
      { text: 'Electrical', value: 'I need electrical work done' },
      { text: 'General Repairs', value: 'I need general repairs done' },
      { text: 'Roofing', value: 'I need roofing work done' }
    ]
  }
];

const MockChatWidget: React.FC<ChatWidgetProps> = ({ isPortalMode = false }) => {
  console.info('🎮 MockChatWidget: Rendering with isPortalMode:', isPortalMode);
  
  const [messages, setMessages] = useState<Message[]>(MOCK_MESSAGES);
  const [isSending, setIsSending] = useState(false);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [previewImageObjects, setPreviewImageObjects] = useState<{ file: File; url: string }[]>([]);
  const [expandedImageUrl, setExpandedImageUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleUserSubmit = async (inputText: string) => {
    if (!inputText.trim() && selectedImages.length === 0) return;

    // Create mock attachment URLs for selected images (in real app, these would be uploaded)
    const mockAttachmentUrls: string[] = selectedImages.length > 0
      ? previewImageObjects.map(img => img.url) // Use preview URLs for mock
      : [];

    // Add user message with both text and images
    const userMessage: Message = {
      type: 'user',
      content: inputText.trim() || '', // Allow empty text if images are present
      attachments: mockAttachmentUrls.length > 0 ? mockAttachmentUrls : undefined,
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsSending(true);

    // Clear images after sending
    setSelectedImages([]);
    setPreviewImageObjects([]);

    // Simulate bot response after delay
    setTimeout(() => {
      let responseContent = '';
      if (inputText.trim() && mockAttachmentUrls.length > 0) {
        responseContent = `Thanks for your message: "${inputText}" and the ${mockAttachmentUrls.length} image(s) you shared. This is a mock response for the portal demo.`;
      } else if (inputText.trim()) {
        responseContent = `Thanks for your message: "${inputText}". This is a mock response for the portal demo.`;
      } else if (mockAttachmentUrls.length > 0) {
        responseContent = `Thanks for sharing ${mockAttachmentUrls.length} image(s). I can see what you're working with. This is a mock response for the portal demo.`;
      }

      const botMessage: Message = {
        type: 'bot',
        content: responseContent,
        timestamp: Date.now(),
        suggestions: [
          { text: 'Tell me more', value: 'Can you tell me more about this service?' },
          { text: 'Get a quote', value: 'I\'d like to get a quote for this work' },
          { text: 'Schedule consultation', value: 'Can we schedule a consultation?' }
        ]
      };
      setMessages(prev => [...prev, botMessage]);
      setIsSending(false);
    }, 1500);
  };

  const handleSuggestionClick = (suggestion: Suggestion) => {
    handleUserSubmit(suggestion.value);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedImages(files);
    
    // Create preview URLs
    const previews = files.map(file => ({
      file,
      url: URL.createObjectURL(file)
    }));
    setPreviewImageObjects(previews);
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setPreviewImageObjects(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      // Clean up URL for removed image
      if (prev[index]) {
        URL.revokeObjectURL(prev[index].url);
      }
      return newPreviews;
    });
  };

  const clearAllImages = () => {
    // Clean up all URLs
    previewImageObjects.forEach(preview => URL.revokeObjectURL(preview.url));
    setSelectedImages([]);
    setPreviewImageObjects([]);
  };

  const triggerFileInput = (ref: React.RefObject<HTMLInputElement>) => {
    ref.current?.click();
  };

  const openExpandedImage = (url: string) => setExpandedImageUrl(url);
  const closeExpandedImage = () => setExpandedImageUrl(null);

  const clearSession = () => {
    setMessages(MOCK_MESSAGES);
    clearAllImages();
  };

  return (
    <>
      {(true || isPortalMode) && (
        <div className={`chat-container open ${isPortalMode ? 'portal-mode' : ''}`} role="complementary" aria-label="Chat window">
          <ChatHeader
            onClose={isPortalMode ? () => window.history.back() : () => console.log('Close chat')}
            onClearSession={clearSession}
            isPortalMode={isPortalMode}
          />
          
          <MessageList
            messages={messages}
            isLoading={isSending}
            queueLength={0}
            onSuggestionClick={handleSuggestionClick}
            onImageClick={openExpandedImage}
            isPortalMode={isPortalMode}
          />

          <ImagePreviewArea
            previewImageObjects={previewImageObjects}
            onRemoveImage={removeImage}
            onClearAllImages={clearAllImages}
            onAddImageClick={() => triggerFileInput(fileInputRef)}
            onImageClick={openExpandedImage}
            imageError={null}
            isUploading={false}
          />
          
          <ChatInputArea
            onSendMessage={handleUserSubmit}
            onAttachClick={() => triggerFileInput(fileInputRef)}
            onCameraClick={() => triggerFileInput(fileInputRef)}
            isSending={isSending}
            hasSelectedImages={selectedImages.length > 0}
            isPortalMode={isPortalMode}
          />
        </div>
      )}

      {expandedImageUrl && (
        <ExpandedImageView imageUrl={expandedImageUrl} onClose={closeExpandedImage} />
      )}

      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileSelect}
        accept="image/*"
        multiple
      />
    </>
  );
};

export default MockChatWidget;
