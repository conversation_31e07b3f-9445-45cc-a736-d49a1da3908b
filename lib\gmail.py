from google.oauth2 import service_account
from googleapiclient.discovery import build
from email.message import EmailMessage
import base64
import os
print(os.getcwd())


SERVICE_ACCOUNT_FILE = 'quoteai-455304-b07869f30cf6.json'
USER_EMAIL = '<EMAIL>'

SCOPES = ['https://www.googleapis.com/auth/gmail.send']


def generate_email_body(full_name, phone_number, email, suburb, preferred_time, issue_summary):
    issue_html = issue_summary.replace('\n', '<br>')
    return f"""
    <html>
    <body>
        <p><a href="https://getquoteai.com">Quote AI</a></p>

        <p><strong>Name:</strong> {full_name}</p>
        <p><strong>Phone Number:</strong> {phone_number}</p>
        <p><strong>Email:</strong> <a href="mailto:{email}">{email}</a></p>
        <p><strong>Suburb:</strong> {suburb}</p>
        <p><strong>Preferred Time:</strong> {preferred_time}</p>

        <p><strong>Outline of Job:</strong><br>
        {issue_html}
        </p>
    </body>
    </html>
    """
def gmail_service():
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE, scopes=SCOPES
    )
    delegated_credentials = credentials.with_subject(USER_EMAIL)
    service = build('gmail', 'v1', credentials=delegated_credentials)
    return service

def send_email(tradie_email, subject, email_content):
    html_body = generate_email_body(
        full_name=email_content.get('full_name',''),
        phone_number=email_content.get("phone_number", ''),
        email=email_content.get('user_email',''),
        suburb=email_content.get('suburb',''),
        preferred_time=email_content.get('preferred_time',''),
        issue_summary=email_content.get('summary_of_issue','')
    )
    message = EmailMessage()
    message.set_content("This is the plain text fallback for clients that don't support HTML.")
    message.add_alternative(html_body, subtype='html')
    message['To'] = tradie_email
    message['From'] = USER_EMAIL
    message['Subject'] = f"{subject} for {email_content.get('full_name', '')}"

    encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
    send_body = {'raw': encoded_message}

    service = gmail_service()
    result = service.users().messages().send(userId="me", body=send_body).execute()
    return {
        "status" : f"Email sent! Message ID: {result['id']}"
    }

# Example usage
# send_email(
#     to="<EMAIL>",
#     subject="Quote AI: Job Submission",
#     body="""
#     Full Name: John Doe
#     Issue: Leaking tap
#     Contact: 0412 345 678
#     Suburb: Bondi
#     Preferred Time: Tuesday Morning
#     """
# )


