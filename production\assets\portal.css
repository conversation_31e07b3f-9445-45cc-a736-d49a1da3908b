/* QuoteAI Portal - Production CSS Bundle */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

#portal-root {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Portal Container */
.portal-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Portal Header - REMOVED for single header design */
.portal-header {
  display: none !important;
}

/* Legacy animations - kept for potential future use */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Enhanced pulse animation for connection status */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes pulseOffline {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* Portal Chat Area - Optimized for single header design */
.portal-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  width: 100%;
  height: 100vh;
  margin: 0 auto;
  background: white;
  border-radius: 0; /* Full screen design */
  box-shadow: none; /* Simplified for performance */
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out both;
  will-change: transform, opacity;
  transform: translateZ(0); /* Force hardware acceleration */
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Chat Container Styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  overflow: hidden;
}

.chat-container.portal-mode {
  position: static !important;
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  bottom: auto !important;
  right: auto !important;
  transform: none !important;
  flex: 1;
}

/* Enhanced Chat Header - Primary header for all devices */
.chat-header {
  padding: 16px;
  background-color: #1b8ae4;
  color: white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 100;
  will-change: transform;
  transform: translateZ(0);
}

.chat-header.portal-mode {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0;
  font-size: 1.1rem;
  padding: 1.25rem 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideInDown 0.6s ease-out;
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.chat-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.chat-title h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chat-title h3::before {
  content: '💬';
  font-size: 1.2em;
  animation: subtleBounce 3s ease-in-out infinite;
}

@keyframes subtleBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.chat-title p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 400;
}

.chat-title p::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s infinite;
  display: inline-block;
  flex-shrink: 0;
}

.chat-title p.offline::before {
  background: #ef4444;
  animation: pulseOffline 2s infinite;
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.icon-button {
  background: none;
  border: none;
  font-size: 20px;
  padding: 4px;
  cursor: pointer;
  border-radius: 20px;
  transition: all 0.3s ease;
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  padding: 16px 8px 8px 16px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 12px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  overscroll-behavior: contain; /* Prevent scroll chaining */
}

.chat-messages.portal-mode {
  flex: 1;
  max-height: none;
  padding: 1.5rem;
  background: #f8fafc;
}

/* Messages */
.message {
  padding: 12px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.3;
  position: relative;
  animation: fadeIn 0.3s ease;
  max-width: 80%;
  margin-bottom: 4px;
}

.message.user {
  background-color: #1b8ae4;
  color: white;
  border-bottom-right-radius: 2px;
  align-self: flex-end;
}

.message.bot {
  background-color: #f1f1f1;
  color: #333;
  border-bottom-left-radius: 2px;
  align-self: flex-start;
}

.message-text {
  margin-bottom: 8px;
}

.message-time {
  font-size: 10px;
  color: #999;
  margin-top: 4px;
  opacity: 0.7;
}

/* Message Attachments */
.message-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.message-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.message-image:hover {
  transform: scale(1.05);
}

/* Message Suggestions */
.message-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.suggestion-button {
  background-color: #f1f1f1;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 8px 14px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.suggestion-button:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999;
  animation: typing-pulse 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-pulse {
  0%, 60%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  30% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Image Preview Area */
.image-preview-area {
  padding: 12px;
  background-color: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  max-height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Prevent flickering */
}

.image-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.clear-images-btn {
  background: none;
  border: none;
  color: #ff4b4b;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.clear-images-btn:hover {
  background-color: rgba(255, 75, 75, 0.1);
}

.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.image-preview-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-preview-item:hover {
  transform: scale(1.05);
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.remove-image-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Chat Input Area */
.chat-input {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background-color: white;
  border-top: 1px solid #e8e8e8;
  position: relative;
}

.chat-input.portal-mode {
  padding: 1rem 1.5rem;
  background: white;
  border-top: 1px solid #e2e8f0;
  border-radius: 0;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-input.portal-mode .input-row {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.chat-input input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.chat-input input:focus {
  border-color: #1b8ae4;
}

/* Portal Textarea */
.portal-textarea {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px;
  max-height: 120px;
  line-height: 1.4;
  font-family: inherit;
  resize: none;
  overflow: hidden;
  box-sizing: border-box;
  width: 0;
}

.portal-textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
  outline: none;
}

.portal-textarea::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Portal Buttons */
.portal-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  flex-wrap: nowrap;
  min-width: fit-content;
}

.chat-input.portal-mode .icon-button {
  padding: 0.75rem;
  font-size: 1.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #374151;
}

.chat-input.portal-mode .icon-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #e2e8f0;
}

.chat-input.portal-mode .send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.chat-input.portal-mode .send-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.chat-input.portal-mode .send-button:disabled {
  background: #cbd5e1;
  color: #64748b;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.chat-input.portal-mode .attach-button:hover {
  background: #dbeafe;
  border-color: #3b82f6;
}

.chat-input.portal-mode .camera-button:hover {
  background: #dcfce7;
  border-color: #22c55e;
}

/* Expanded Image Modal */
.expanded-image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expanded-image-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  cursor: pointer;
}

.expanded-image-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  z-index: 10001;
}

.expanded-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.close-expanded-btn {
  position: absolute;
  top: -40px;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-expanded-btn:hover {
  background: white;
}

/* Mobile Responsive Styles - Optimized for single header */
@media (max-width: 768px) {
  /* Portal header remains hidden */
  .portal-header {
    display: none !important;
  }

  /* Optimize portal chat area for mobile */
  .portal-chat-area {
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
    animation: none; /* Disable animations on mobile for better performance */
  }

  /* Mobile-optimized chat header */
  .chat-header.portal-mode {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    animation: none; /* Disable slide animation on mobile */
  }

  .chat-header.portal-mode .chat-title h3 {
    font-size: 1rem;
  }

  .chat-header.portal-mode .chat-title h3::before {
    font-size: 1.1em;
    animation: none; /* Disable bounce on mobile for performance */
  }

  .chat-header.portal-mode .chat-title p {
    font-size: 0.8rem;
  }

  .chat-header.portal-mode .chat-actions {
    gap: 0.375rem;
  }

  .chat-input.portal-mode {
    padding: 1rem;
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    overflow: hidden;
  }

  .chat-input.portal-mode .input-row {
    gap: 0.5rem;
    max-width: 100%;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
    max-height: 100px;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.625rem;
    font-size: 1.125rem;
    min-width: 40px;
    min-height: 40px;
    flex-shrink: 0;
  }

  .chat-messages.portal-mode {
    padding: 1rem;
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
  }

  .image-preview-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  /* Portal header remains hidden */
  .portal-header {
    display: none !important;
  }

  /* Compact chat header for small screens */
  .chat-header.portal-mode {
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
  }

  .chat-header.portal-mode .chat-title h3 {
    font-size: 0.95rem;
  }

  .chat-header.portal-mode .chat-title h3::before {
    font-size: 1em;
  }

  .chat-header.portal-mode .chat-title p {
    font-size: 0.75rem;
  }

  .chat-header.portal-mode .chat-actions {
    gap: 0.25rem;
  }

  .chat-input.portal-mode {
    padding: 0.75rem;
    padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
  }

  .chat-input.portal-mode .input-row {
    gap: 0.375rem;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px;
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
    max-height: 80px;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.5rem;
    font-size: 1rem;
    min-width: 36px;
    min-height: 36px;
    border-radius: 8px;
  }

  .chat-input.portal-mode .portal-buttons {
    gap: 0.25rem;
    flex-shrink: 0;
  }

  .chat-messages.portal-mode {
    padding: 0.75rem;
  }

  .chat-header.portal-mode {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .image-preview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .chat-input.portal-mode .icon-button:hover {
    transform: none;
    box-shadow: none;
  }

  .chat-input.portal-mode .icon-button:active {
    transform: scale(0.95);
    background: #e2e8f0;
  }

  .chat-input.portal-mode .send-button:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }

  .chat-input.portal-mode .attach-button:active {
    background: #dbeafe;
    border-color: #3b82f6;
  }

  .chat-input.portal-mode .camera-button:active {
    background: #dcfce7;
    border-color: #22c55e;
  }
}

/* Landscape Orientation - Ultra-compact for maximum chat space */
@media (max-width: 768px) and (orientation: landscape) {
  /* Portal header remains hidden */
  .portal-header {
    display: none !important;
  }

  /* Ultra-compact header for landscape */
  .chat-header.portal-mode {
    padding: 0.5rem 1rem;
    min-height: 60px;
  }

  .chat-header.portal-mode .chat-title h3 {
    font-size: 0.9rem;
  }

  .chat-header.portal-mode .chat-title h3::before {
    font-size: 0.9em;
  }

  .chat-header.portal-mode .chat-title p {
    font-size: 0.7rem;
  }

  .chat-header.portal-mode .chat-actions {
    gap: 0.25rem;
  }
}

  .chat-input.portal-mode {
    padding: 0.75rem 1rem;
  }

  .chat-messages.portal-mode {
    padding: 0.75rem 1rem;
  }

/* Accessibility */
.chat-input.portal-mode .icon-button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth Transitions - Optimized for Mobile */
.chat-input.portal-mode .portal-textarea {
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  /* Remove height transition to prevent mobile layout issues */
}

/* Mobile-specific textarea optimizations */
@media (max-width: 768px) {
  .chat-input.portal-mode .portal-textarea {
    transition: none; /* Disable all transitions on mobile for better performance */
    will-change: auto; /* Reset will-change to prevent unnecessary compositing */
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .chat-input.portal-mode .icon-button {
    transition: none;
  }

  .chat-input.portal-mode .portal-textarea {
    transition: none;
  }

  .portal-container {
    animation: none;
  }

  .portal-header {
    animation: none;
  }

  .portal-chat-area {
    animation: none;
  }
}

/* Global portal container optimizations */
.portal-container {
  /* Remove top padding since portal header is removed */
  padding-top: 0 !important;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Performance optimizations for all devices */
@media (max-width: 768px) {
  .portal-container {
    animation: none; /* Disable container animations on mobile */
  }

  /* Ensure optimal mobile layout */
  .portal-chat-area {
    height: 100vh;
    max-height: 100vh;
    flex: 1;
  }
}

/* Desktop optimizations */
@media (min-width: 769px) {
  .portal-container {
    max-width: none; /* Remove max-width to prevent clipping */
    margin: 0;
    width: 100%;
  }

  .portal-chat-area {
    border-radius: 0; /* Remove border radius for full-screen experience */
    box-shadow: none; /* Remove shadow for cleaner look */
    margin: 0;
    height: 100vh;
  }

  .chat-header.portal-mode {
    border-radius: 12px 12px 0 0;
  }
}
