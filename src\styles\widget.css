/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Open Sans', sans-serif;
    background-color: #f4f4f4;
    color: #333;
  }

  /* Floating button */
  .chat-button {
    position: fixed !important; /* Force fixed position */
    bottom: 20px !important; /* Force bottom position */
    right: 20px !important; /* Force right position */
    padding: 10px 12px;
    border: none;
    border-radius: 14px;
    background-color: #1b8ae4; /* Main color */
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999; /* Ensure it's above other elements */
    width: auto; /* Prevent stretching */
    min-width: 180px; /* Minimum width for the button */
    max-width: 300px; /* Maximum width */
    left: auto !important; /* Prevent left positioning */
  }

  /* Notification bubble */
  .notification-bubble {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ff4b4b; /* Red color for notifications */
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: notification-pulse 1.5s infinite;
  }

  /* Notification pulse animation */
  @keyframes notification-pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 75, 75, 0.7);
    }
    70% {
      transform: scale(1.1);
      box-shadow: 0 0 0 10px rgba(255, 75, 75, 0);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 75, 75, 0);
    }
  }

  /* Button loading indicator */
  .button-loading-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    padding: 2px 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    z-index: 10;
  }

  .button-loading-indicator .dot {
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
    display: inline-block;
    animation: dot-pulse 1.5s infinite ease-in-out;
    box-shadow: 0 0 2px rgba(255, 255, 255, 0.5);
  }

  .button-loading-indicator .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .button-loading-indicator .dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes dot-pulse {
    0%, 100% {
      transform: scale(0.8);
      opacity: 0.6;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  .chat-button:hover {
    background-color: #53a2Be; /* Main color 2 */
    transform: scale(1.1);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
  }

  /* Chat window */
  .chat-container {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 350px;
    height: 560px;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    background-color: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
  }

  .chat-container.open {
    transform: translateY(0);
    opacity: 1;
  }

  /* Chat header */
  .chat-header {
    padding: 16px;
    background-color: #1b8ae4; /* Main color */
    color: white;
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .chat-icon {
    width: 28px;
    height: 28px;
    background-color: white;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
  }

  .close-button, .clear-button {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 4px;
    border-radius: 4px;
  }

  .close-button {
    font-size: 24px;
  }

  .close-button:hover {
    transform: rotate(90deg);
  }

  .clear-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }

  /* Chat messages area */
  .chat-messages {
    flex: 1;
    padding: 16px 8px 8px 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    scroll-behavior: smooth;
  }

  /* Message container */
  .message-container {
    display: flex;
    flex-direction: column;
    max-width: 80%;
    margin-bottom: 4px;
  }

  /* Chat bubbles */
  .message {
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.3;
    position: relative;
    animation: fadeIn 0.3s ease;
    width: 100%;
  }

  .message-content {
    width: 100%;
  }

  .message.user {
    background-color: #1b8ae4; /* Main color */
    color: white;
    border-bottom-right-radius: 2px;
  }

  .message.bot {
    background-color: #f1f1f1;
    color: #333;
    border-bottom-left-radius: 2px;
  }

  /* Message container alignment */
  .user-container {
    align-self: flex-end;
  }

  .bot-container {
    align-self: flex-start;
  }

  /* Message timestamp */
  .message-timestamp {
    font-size: 10px;
    color: #999;
    margin-top: 2px;
    padding: 0 4px;
  }

  .message-timestamp.user {
    align-self: flex-end;
    text-align: right;
  }

  .message-timestamp.bot {
    align-self: flex-start;
    text-align: left;
  }

  /* Suggestion bubbles */
  .suggestions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
    margin-bottom: 8px;
    width: 100%;
    align-self: flex-start;
  }

  .suggestion-bubble {
    background-color: #f1f1f1;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 18px;
    padding: 8px 14px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  .suggestion-bubble:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .suggestion-bubble:active {
    transform: translateY(0);
    background-color: #d0d0d0;
  }

  /* Input area */
  .chat-input {
    display: flex;
    flex-direction: column;
    padding: 12px;
    background-color: white;
    border-top: none;
    position: relative;  /* Make sure we can position the pseudo-element */
  }

  .chat-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 90%;
    height: 1px;
    background-color: #e8e8e8;
    transform: translateX(-50%);
  }

  .chat-input input {
    flex: 1;
    padding: 10px;
    border: none;
    background-color: transparent;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
  }

  .chat-input input:focus {
    border-color: #1b8ae4; /* Main color */
  }

  .icons {
    padding: 0 2px;
    display: flex;
    justify-content: left;
    gap: 10px;
    margin-top: 5px; /* Add spacing between input and icons */
  }

  .icon-button {
    background: none;
    border: none;
    font-size: 20px;
    padding: 4px;
    cursor: pointer;
    border-radius: 20px;
    transition: background-color 0.3s ease-in-out, transform 0.3s ease-in-out;
  }

  .send-button {
    color: #1b8ae4;
    font-size: 18px;
    margin-left: 4px;
  }

  .send-button:disabled {
    color: #ccc;
    cursor: not-allowed;
  }

  .icon-button:disabled:hover {
    background-color: transparent;
    transform: none;
  }

  .chat-input button:hover {
    background-color: #1b8ae4; /* Main color 2 */
    transform: scale(1.2); /* Slight growth effect */
    border: 12px;
  }

  /* Tooltip Effect */
  .icon-button::after {
    content: attr(data-tooltip); /* Uses the data-tooltip attribute */
    position: absolute;
    bottom: 34px; /* Position above the button */
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 10px;
    padding: 5px 10px;
    border-radius: 5px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  }

  /* Show tooltip on hover */
  .icon-button:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px); /* Slight lift effect */
  }

  /* Hover effect for button */
  .icon-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.2);
  }


  /* Image preview container */
  .image-preview-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background-color: #f9f9f9;
    border-top: 1px solid #e8e8e8;
    max-height: 250px; /* Increased height for better visibility */
    overflow-y: auto;
  }

  /* Image preview header with count and clear button */
  .image-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
  }

  .image-count {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }

  .clear-all-btn {
    background: none;
    border: none;
    color: #ff4b4b;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .clear-all-btn:hover {
    background-color: rgba(255, 75, 75, 0.1);
  }

  /* Image grid layout */
  .image-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4 images per row */
    gap: 8px;
  }

  .image-preview {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1; /* Square aspect ratio */
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer; /* Indicate it's clickable */
    transition: transform 0.2s ease;
  }

  .image-preview:hover {
    transform: scale(1.05);
  }

  .image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .remove-image-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    z-index: 2; /* Ensure it's above the image */
  }

  .remove-image-btn:hover {
    background-color: rgba(255, 0, 0, 0.8);
  }

  /* Add image box */
  .add-image-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    aspect-ratio: 1/1;
    border: 2px dashed #ccc;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .add-image-box:hover {
    background-color: rgba(27, 138, 228, 0.1);
    border-color: #1b8ae4;
  }

  .add-icon {
    font-size: 24px;
    color: #999;
    margin-bottom: 4px;
  }

  .add-text {
    font-size: 12px;
    color: #666;
  }

  /* Expanded image view */
  .expanded-image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000; /* Above everything */
    cursor: pointer;
    animation: fadeIn 0.3s ease;
  }

  .expanded-image-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: zoomIn 0.3s ease;
  }

  .expanded-image-container img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    display: block;
  }

  .close-expanded-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .close-expanded-btn:hover {
    background-color: rgba(255, 0, 0, 0.8);
    transform: rotate(90deg);
  }

  @keyframes zoomIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Message attachments */
  .message-attachments {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
  }

  .message-attachment-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .message-attachment-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    transition: background 0.3s ease;
  }

  .message-attachment-container:hover::after {
    background: rgba(0, 0, 0, 0.2);
  }

  .message-attachment-container:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .message-attachment-img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    display: block;
    cursor: pointer;
  }

  /* Make images smaller on mobile */
  @media (max-width: 480px) {
    .message-attachment-img {
      width: 80px;
      height: 80px;
    }
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

 /* Loading dots */
.loading-dots {
    font-size: 20px;
    color: #53a2Be; /* Main color 2 */
    animation: loadingDots 1.5s infinite steps(1, end); /* Corrected animation name */
  }

  /* Queue indicator */
  .queue-indicator {
    background-color: rgba(27, 138, 228, 0.1);
    color: #1b8ae4;
    font-size: 12px;
    padding: 6px 10px;
    border-radius: 4px;
    margin: 8px auto;
    text-align: center;
    max-width: 200px;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  .loading-dots span {
    display: inline-block;
    margin: 0 2px;
  }

  /* Loading dots animation */
  @keyframes loadingDots {
    0% {
      opacity: 0;
    }
    33% {
      opacity: 1;
    }
    66% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }

  .loading-dots {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
    color: #53a2Be;  /* Accent color */
    font-family: 'Open Sans', sans-serif;
    padding: 12px;
    border-radius: 12px;
    background-color: #f4f4f4;
    align-self: flex-start; /* Align left like the bot message */
    max-width: 80%;
    margin-right: 12px;
    margin-left: 12px;
    word-wrap: break-word;
  }

  .loading-dots span {
    animation: loadingDots 1.5s infinite;
  }

  .loading-dots span:nth-child(1) {
    animation-delay: 0s;
  }

  .loading-dots span:nth-child(2) {
    animation-delay: 0.5s;
  }

  .loading-dots span:nth-child(3) {
    animation-delay: 1s;
  }

  /* Responsive styles for mobile devices */
  @media (max-width: 768px) {
    .chat-container {
      width: 90%;
      height: 80vh;
      right: 5%;
      bottom: 80px;
    }

    .chat-button {
      right: 20px !important; /* Keep consistent right positioning */
      bottom: 20px !important; /* Keep consistent bottom positioning */
      justify-content: center;
      width: auto; /* Keep auto width */
      min-width: 180px; /* Maintain minimum width */
      max-width: 250px; /* Maximum width on mobile */
    }

    .image-preview-container {
      max-height: 200px;
    }

    /* 3 images per row on tablets */
    .image-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .message-attachment-img {
      width: 70px;
      height: 70px;
    }
  }

  @media (max-width: 480px) {
    .chat-container {
      width: 95%;
      height: 85vh;
      right: 2.5%;
      bottom: 70px;
    }

    .chat-button {
      right: 10px !important; /* Slightly closer to edge on small screens */
      bottom: 10px !important; /* Slightly closer to bottom on small screens */
      width: auto; /* Keep auto width */
      min-width: 160px; /* Slightly smaller minimum width for very small screens */
      max-width: 220px; /* Maximum width on small mobile */
      padding: 8px 10px; /* Slightly smaller padding */
    }

    /* 2 images per row on phones */
    .image-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 6px;
    }

    .image-preview-container {
      padding: 8px;
    }

    .image-preview-header {
      margin-bottom: 6px;
      padding-bottom: 6px;
    }

    .image-preview {
      width: 100%;
    }

    .message-attachment-img {
      width: 60px;
      height: 60px;
    }
  }